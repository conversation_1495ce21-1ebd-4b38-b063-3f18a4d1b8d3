from os import environ
import sys
from fastapi import FastAPI
from app.api.v1.endpoints import similarity, photo_upload
from app.core.config import settings
from app.middleware.middleware import TokenValidationMiddleware
from app.middleware.request_logging import RequestLoggingMiddleware
from app.dependencies.dependencies import get_sync_service, container, get_clustering_service
import logging

# Configure logging
logging.basicConfig(
    level=settings.LOG_LEVEL,
    format=settings.LOG_FORMAT
)

# Wire up the container
container.wire(modules=[
    "app.api.v1.endpoints.similarity",
    "app.api.v1.endpoints.photo_upload",
    "app.dependencies.dependencies"
])

if environ.get('RUN_CLUSTER'):
    get_clustering_service().build_cluster()    
    sys.exit()

sync = get_sync_service()
if sync.run_sync_job():    
    get_clustering_service().build_cluster()
    sys.exit()  # immediately kill the app after sync

app = FastAPI(title=settings.APP_NAME)

# Add middlewares - order matters, add RequestLoggingMiddleware before other middlewares
app.add_middleware(RequestLoggingMiddleware)
app.add_middleware(TokenValidationMiddleware)

# Include routers
app.include_router(similarity.router, prefix="/v1")
app.include_router(photo_upload.router, prefix="/v1")

# Add container instance to app
app.container = container