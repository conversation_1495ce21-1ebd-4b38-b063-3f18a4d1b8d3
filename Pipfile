[[source]]
url = "https://pypi.org/simple"
verify_ssl = true
name = "pypi"

[packages]
fastapi = "*"
uvicorn = {extras = ["standard"], version = "*"}
annoy = "*"
pillow = "*"
pydantic = "*"
memory-profiler = "*"
pymongo = "*"
pymilvus = "*"
redis = {extras = ["hiredis"], version = "*"}
pydantic-settings = "*"
dependency-injector = "*"
tensorflow-cpu = "==2.16.2"
tensorflow-hub = "==0.16.1"
scikit-learn = "*"
torch = "*"
torchvision = "*"

[dev-packages]

[requires]
python_version = "3.12"
