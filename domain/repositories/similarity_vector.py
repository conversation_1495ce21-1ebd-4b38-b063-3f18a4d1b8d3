import abc
from typing import Optional

class SimilarityVector(abc.ABC):

    @abc.abstractmethod
    def save(self, vector_data):
        """
        Save image feature to vector db.
        """
        raise NotImplementedError

    @abc.abstractmethod
    def find_nearest_image(self, img_feature, top_n = 15):
        raise NotImplementedError


    @abc.abstractmethod
    def fetch_all(self):
        raise NotImplementedError