import abc
from typing import Optional, List, Dict, Any
from domain.entities.photo import Photo

class SimilarityRepository(abc.ABC):
    """
    An abstract class for similarity repository.
    """

    @abc.abstractmethod
    def fetch_image_detail(self, url) -> Optional[Photo]:
        """
        Fetch photo information by url
        """
        raise NotImplementedError

    @abc.abstractmethod
    def fetch_image_by_index(self, index) -> Optional[str]:
        raise NotImplementedError

    @abc.abstractmethod
    async def batch_insert_photos(self, photos: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Insert multiple photos in a batch operation.
        
        Args:
            photos: List of photo data dictionaries to insert
            
        Returns:
            Dict containing operation results and statistics
        """
        raise NotImplementedError
