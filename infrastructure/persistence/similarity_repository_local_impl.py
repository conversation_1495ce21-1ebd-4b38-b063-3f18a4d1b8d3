from typing import Optional, List, Dict, Any
from dataclasses import asdict
import numpy as np
import urllib.parse
from domain.repositories.similarity_repository import SimilarityRepository
from domain.entities.photo import Photo, Event, User
import json


class SimilarityRepoLocal(SimilarityRepository):
    def __init__(self):
        self.img_urls = np.load('model/sfi_url.npz')['urls']
        self.photos = self.load_photos()
        self.profiles = self.load_profiles()
        print('model and data loaded...')
        print(f'-- total imgage urls: {len(self.img_urls)}')
        print(f'-- total photos: {len(self.photos)}')


    def fetch_image_detail(self, url) -> Optional[Photo]:
        for photo in self.photos:
            if photo['url'] == url:
                user = self.find_user(photo['user_id'])
                if user is None:
                    print('can not find user with id: ', photo['user_id'])
                    return None

                event = Event(
                    year=photo['tahun'],
                    name=photo['event_name'],
                    short_name=photo['event_shortname']
                )

                user_data = User(
                    name=user['nama_lengkap'],
                    honors=user['gelar_fotografi'],
                    club=user['klub'],
                    fp_id=user['id'],
                    email=user['email']
                )

                photo_data = Photo(
                    title=photo['judul_foto'],
                    category=photo['kategori'],
                    event=event,
                    user=user_data,
                    meta_data=photo.get('meta_data')
                )
                return photo_data

        return None

    def fetch_image_by_index(self, index) -> Optional[str]:
        return self.img_urls[index]

    def find_user(self, id):
        for user in self.profiles:
            if user['id'] == id:
                return user
        return None

    def load_photos(self):
        with open('model/photos.json') as f:
            photos = json.load(f)

        result = []
        for photo in photos:
            if 'results' not in photo: continue                
            for img in photo['results']:
                if 'url' not in img:
                    img['url'] = f'https://simfoni.fpsi.or.id/data/accepted_photo/{img["folder"]}/{urllib.parse.quote(img["filename"])}'                
                img['user_id'] = photo['query']['id']
                result.append(img)
        return result


    def load_profiles(self):
        with open('model/profiles.json') as f:
            profiles = json.load(f)

        result = []
        for profile in profiles:
            if 'results' not in profile: continue     
            result.append(profile['results'])           
        return result

    async def batch_insert_photos(self, photos: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        Insert multiple photos in a batch operation.
        
        Args:
            photos: List of photo data dictionaries to insert
            
        Returns:
            Dict containing operation results and statistics
        """
        successful = 0
        failed = 0
        failed_details = []
        
        try:
            # Load existing photos
            existing_photos = self.load_photos()
            
            # Process each photo
            for photo in photos:
                try:
                    # Validate required fields
                    required_fields = ['id', 'url', 'features']
                    if not all(field in photo for field in required_fields):
                        raise ValueError(f"Missing required fields: {required_fields}")
                    
                    # Check if photo already exists
                    if any(p['url'] == photo['url'] for p in existing_photos):
                        raise ValueError("Photo with this URL already exists")
                    
                    # Add to existing photos
                    existing_photos.append(photo)
                    successful += 1
                    
                except Exception as e:
                    failed += 1
                    failed_details.append({
                        'photo_id': photo.get('id', 'unknown'),
                        'error': str(e)
                    })
                    continue
            
            # Save updated photos back to file
            if successful > 0:
                with open('model/photos.json', 'w') as f:
                    json.dump(existing_photos, f, indent=2)
            
            return {
                'status': 'success' if failed == 0 else 'partial',
                'message': 'All photos inserted successfully' if failed == 0 else 'Some photos failed to insert',
                'details': {
                    'total_processed': len(photos),
                    'successful': successful,
                    'failed': failed,
                    'failed_details': failed_details
                }
            }
            
        except Exception as e:
            return {
                'status': 'error',
                'message': f'Batch insert failed: {str(e)}',
                'details': {
                    'total_processed': len(photos),
                    'successful': successful,
                    'failed': failed,
                    'failed_details': failed_details
                }
            }
