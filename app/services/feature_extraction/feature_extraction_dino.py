
import torch
from torchvision import transforms
from PIL import Image
import numpy as np
import logging
from typing import Optional, List, Union
from utils.image import load_image_url, load_images
from app.interfaces.feature_extraction_interface import IFeatureExtractionService

class DinoFeatureExtractionService(IFeatureExtractionService):

    def __init__(self):
        # Load DINOv2 model using torch.hub
        self.model = torch.hub.load('facebookresearch/dinov2', 'dinov2_vitb14')

        # Define transform for preprocessing
        self.transform = transforms.Compose([
            transforms.Resize(224),
            transforms.CenterCrop(224),
            transforms.ToTensor(),
            transforms.Normalize(mean=[0.5, 0.5, 0.5], std=[0.5, 0.5, 0.5])
        ])

    def extract_features(self, image: Optional[np.ndarray] = None, image_url: Optional[str] = None, image_urls: Optional[List[str]] = None) -> Union[np.ndarray, List[np.ndarray]]:
        try:
            # Validate input - exactly one parameter should be provided
            provided_params = sum([image is not None, image_url is not None, image_urls is not None])
            if provided_params == 0:
                raise ValueError("One of image, image_url, or image_urls must be provided")
            if provided_params > 1:
                raise ValueError("Only one of image, image_url, or image_urls should be provided")

            images = []
            # Handle batch processing for multiple URLs
            if image_urls is not None:
                images = load_images(image_urls)

            # Handle single image processing
            if image is None and image_url is not None:
                image = load_images([image_url])[0]
                images.append(image)
                if isinstance(image, dict) and 'error' in image:
                    raise ValueError(f"Failed to load image from URL: {image['error']}")
                if image == None:
                    raise ValueError(f"Failed to load image from URL: {image_url}")

            # Process single image
            inputs = [self.transform(img) for img in images]
            inputs = torch.stack(inputs)

            # Extract features
            with torch.no_grad():
                features = self.model(inputs)
            return features
        except Exception as e:
            logging.error(f'[ERROR] extract image with dinoV2 failed: {e}')
            return np.array([])