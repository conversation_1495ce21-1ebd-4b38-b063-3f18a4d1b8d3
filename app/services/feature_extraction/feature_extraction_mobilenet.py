from typing import Optional, List, Union
import numpy as np
import tensorflow as tf
import tensorflow_hub as hub
import time
import logging
from utils.image import load_image_url, load_images
from app.interfaces.feature_extraction_interface import IFeatureExtractionService

logging.basicConfig(level=logging.INFO,
                    format='%(levelname)s %(name)s %(funcName)s %(lineno)d: %(message)s')

class FeatureExtractionService(IFeatureExtractionService):
    def __init__(self):
        # https://www.kaggle.com/models/google/mobilenet-v3/tensorFlow2/large-100-224-feature-vector
        module_handle = "https://tfhub.dev/google/imagenet/mobilenet_v3_large_100_224/feature_vector/5"
        logging.info('>>>> load the model...')
        start = time.time()
        self.module = hub.load(module_handle)
        logging.info(f"module loaded {time.time() - start}")

    def extract_features(self, image: Optional[np.ndarray] = None, image_url: Optional[str] = None, image_urls: Optional[List[str]] = None) -> Union[np.ndarray, List[np.ndarray]]:
        try:
            # Validate input - exactly one parameter should be provided
            provided_params = sum([image is not None, image_url is not None, image_urls is not None])
            if provided_params == 0:
                raise ValueError("One of image, image_url, or image_urls must be provided")
            if provided_params > 1:
                raise ValueError("Only one of image, image_url, or image_urls should be provided")

            # Handle batch processing for multiple URLs
            if image_urls is not None:
                return self._extract_features_batch(image_urls)

            # Handle single image processing
            if image is None and image_url is not None:
                image = load_image_url(image_url)
                if isinstance(image, dict) and 'error' in image:
                    raise ValueError(f"Failed to load image from URL: {image['error']}")
                if len(image) == 0:
                    raise ValueError(f"Failed to load image from URL: {image_url}")

            # Extract features from the image
            feature = self.module(image)
            return np.squeeze(feature)
        except Exception as e:
            logging.error(f'[ERROR] extract image failed: {e}')
            return np.array([])

    def _extract_features_batch(self, image_urls: List[str]) -> List[np.ndarray]:
        """Extract features from multiple image URLs"""
        try:
            results = []

            for i, url in enumerate(image_urls):
                try:
                    # Load single image
                    image = load_image_url(url)

                    if isinstance(image, dict) and 'error' in image:
                        logging.warning(f"Failed to load image at index {i}: {url}, error: {image['error']}")
                        results.append(np.array([]))
                        continue

                    if len(image) == 0:
                        logging.warning(f"Empty image at index {i}: {url}")
                        results.append(np.array([]))
                        continue

                    # Extract features
                    feature = self.module(image)
                    results.append(np.squeeze(feature))

                except Exception as e:
                    logging.warning(f"Failed to extract features for image at index {i}: {url}, error: {e}")
                    results.append(np.array([]))
                    continue

            return results

        except Exception as e:
            logging.error(f'[ERROR] batch extract images with MobileNet failed: {e}')
            return [np.array([]) for _ in image_urls]