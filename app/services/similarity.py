from asyncio import threads
from concurrent.futures import thread
from typing import List, Union, Dict, Any, Optional
from utils.image import load_image_url
from utils.distance import cosine, calculate_similarities
from utils.cast import convert_numpy_types
from utils.request import send_with_retries
import numpy as np
import tensorflow as tf
import tensorflow_hub as hub
import json
import urllib.parse
import requests
import time
from models.SimilarityModel import ImageUrl, Webhook
from domain.repositories.similarity_vector import SimilarityVector
from domain.repositories.similarity_repository import SimilarityRepository
from models.SimilarityModel import SimilarityRequest, SimilarityResponseBatch, SimilarityResultBatch, SimilarityResult
from multiprocessing import Process, Queue
import threading
import logging
from utils.progress import update_progress
from domain.usecases.similarity_usecase import SimilarityUseCase
from domain.repositories.vector_cache_repository import VectorCache
import ast 
from utils import extract
from app.core.config import settings
from app.interfaces.similarity_interface import ISimilarityService
from app.utils.similarity import calculate_similarity_matrix
from app.interfaces.feature_extraction_interface import IFeatureExtractionService

# Create and configure logger
logging.basicConfig(level=logging.INFO,
                    format='%(levelname)s %(name)s %(funcName)s %(lineno)d: %(message)s')


class SimilarityService(ISimilarityService, SimilarityUseCase):    
    def __init__(self, repo: SimilarityRepository, repoVector: SimilarityVector, repoCache: VectorCache, feature_service: IFeatureExtractionService):
        self.repo = repo
        self.repo_vector = repoVector
        self.repo_cache = repoCache
        self.feature_service = feature_service
        self.max_thread = 20

    def extract_image_feature(self, image: Optional[np.ndarray] = None, image_url: Optional[str] = None) -> np.ndarray:
        log = ""
        try:
            # Validate input
            if image is None and image_url is None:
                raise ValueError("Either image or image_url must be provided")

            # If we have a URL, try to get from cache first
            if image_url is not None:
                cached_feature = self._get_feature_cache(extract.url_only(image_url))
                if len(cached_feature) > 0 and cached_feature.shape[0] != 0:
                    log += "feature retrieved from cache..."
                    return cached_feature

            # Use the feature extraction service
            feature = self.feature_service.extract_features(image, image_url)
            log += "feature extracted..."

            # If we have a URL, save the feature to cache
            if image_url is not None and feature.shape[0] != 0:
                self.repo_cache.save(extract.url_only(image_url), json.dumps(feature.tolist()))

            return feature
        except Exception as e:
            print(f'[ERROR] extract image failed: {e}, logs: {log}')
        return np.array([])

    def find_nearest_image(self, img, min_score = 0.7):
        result = []
        nearests = self.repo_vector.find_nearest_image(img)
        for nr in nearests:
            score = cosine(img, nr['feature'])  
            if score < min_score: continue

            result.append({'url': nr['url'], 'similarity': float("{:.2f}".format(score)),'detail': self.repo.fetch_image_detail(nr['url'])})
        return result

    def get_similar_images(self, url: str, min_score: float) -> Dict[str, Any]:
        start = time.time() 
        log = {}     
        
        # Validate URL format first
        try:
            parsed_url = urllib.parse.urlparse(url)
            if not all([parsed_url.scheme, parsed_url.netloc]):
                return {'error_message': f'Invalid URL format: {url}'}
        except Exception as e:
            return {'error_message': f'URL parsing error: {str(e)}'}           

        try: 
            img_url = extract.url_only(url)        
            img_feature = self.extract_image_feature(image_url=url)        
            log['get_image_feature'] = time.time() - start     
            
            if  img_feature.shape[0] == 0:
                logging.info(f"image feature shape is not as expected, shape: {img_feature.shape}, image: {url}")
                return {'error_message': 'failed to extract feature from image, make sure image is publicly accessible'}

            start = time.time() 
            result = self.find_nearest_image(img_feature, min_score)
            log['find_nearest_image'] = time.time() - start
            logging.info(f'get_similar_images, url: {url}, log: {log}')
            return result
        except requests.exceptions.RequestException as e:
            logging.error(f'Network error while fetching image: {url}, error: {str(e)}')
            return {'error_message': f'Network error: {str(e)}'}
        except OSError as e:
            logging.error(f'OS error while processing image: {url}, error: {str(e)}')
            return {'error_message': f'Image processing error: {str(e)}'}
        except Exception as e:
            logging.error(f'Unexpected error processing image: {url}, img_feature shape: {img_feature.shape}, type {type(img_feature)}, log: {log}, error: {str(e)},')            
            return {'error_message': f'Internal error: {str(e)}'}


    def get_similarity_concurency(self,  url, min_score, result):
        similar_imgs = self.get_similar_images(url, min_score)
        if 'error_message' in similar_imgs:
            result.append({'url_original': url, 'message': similar_imgs["error_message"], 'similar_images': [], 'error': True})
        else:
            result.append({'url_original': url, 'similar_images': similar_imgs})

    def get_feature_concurency(self, img_url, result):
        url = extract.url_only(img_url)
        img_feature = self._get_feature_cache(url)
        if len(img_feature) == 0:
            try:          
                img_feature = self.extract_image_feature(image_url=url)
                if len(img_feature) == 0:
                    return {'error_message': f'Failed to extract features from image: {url}'}                    
            except requests.exceptions.RequestException as e:
                logging.error(f'Network error while fetching image: {url}, error: {str(e)}')
                return {'error_message': f'Network error: {str(e)}'}                
            except OSError as e:
                logging.error(f'OS error while processing image: {url}, error: {str(e)}')
                return {'error_message': f'Image processing error: {str(e)}'}
            except Exception as e:
                logging.error(f'Unexpected error processing image: {url}, error: {str(e)}')
                return {'error_message': f'Internal error: {str(e)}'}
        # result.append({'url': img_url, 'feature': img_feature})
        result[img_url] = img_feature

    def get_similar_image_batch(self, request: SimilarityRequest, task_id: str = None) -> List[Dict[str, Any]]:
        update_progress(task_id, 0.0, prefix='progress_')
        result = []  
        time_start = time.time()

        urls, meta_data = self._parse_request(request.urls, request.image_urls)
        if len(urls) == 0:
            logging.warning('--- No Image Being sent ----')
            return []

        #handle compare
        compare_images = {}
        if request.image_urls is not None:
            for img in request.image_urls:
                if img.compare is not None and len(img.compare) > 0:
                    compare_images[img.url] = img.compare
        
        threads_compare = []
        compare_result = {}
        for url in compare_images.keys():
            compare_url = [c.url for c in compare_images[url]]
            compare_meta_data = {c.url:c.meta_data for c in compare_images[url]}
            # print('compare: ', compare_url)

            t = threading.Thread(target=self.__compare_images_concurrency, args=[url, compare_url, compare_meta_data, request.min_score, compare_result])
            t.start()
            threads_compare.append(t)

        threads = []        
        # q = Queue()
        for i, url in enumerate(urls):
            t = threading.Thread(target=self.get_similarity_concurency, args=[url, request.min_score, result])
            t.start()
            threads.append(t)

            if len(threads) == self.max_thread or i == len(urls)-1:
                # logging.info(f'>> wait {len(threads)} threads.... index {i}')
                for t in threads:
                    t.join()

                logging.info(f'>> wait finish of {len(threads)} ... index {i} / {len(urls)} - {(i/len(urls))*100}%')
                update_progress(task_id, i/len(urls), prefix='progress_')
                threads = []
        
        logging.info("wait thread compare...")
        for t in threads_compare:
            t.join()

        # logging.info("append compare and meta_data to result")
        for i,raw in enumerate(result):
            url = raw['url_original']
            if url in compare_result and len(compare_result[url]) > 0:
                result[i]['similar_images'].append(compare_result[url])
            if url in meta_data:
                result[i]['meta_data'] = meta_data[url]

        logging.info(f'\n<<FINISH>> {len(urls)} urls took: {time.time() - time_start} \n')        
        if request.webhook == None or request.webhook.url == '':                   
            return result                

        # Convert numpy types to native Python types for JSON serialization
        converted_result = convert_numpy_types(result)
        body = {'message': 'success', 'result': converted_result}
        update_progress(task_id, 1.0, prefix='progress_')
        # update_progress(f'result_{task_id}', json.dumps(body))

        try:
            print(f'send webhook "{request.webhook.url}"')     
            resp = send_with_retries(request.webhook.url, body, max_retries=3)
            print(f'>>> webhook sent: {request.webhook.url} | code: {resp.status_code} | body: {body}')
        except Exception as e:
            logging.error(f'>>> post webhook error: {request.webhook.url} | err: {e} | body: {body}')            
        return result


    def get_similar_image_compare(self, request: SimilarityRequest) -> SimilarityResponseBatch:
        urls, meta_data = self._parse_request(request.urls, request.image_urls)
        print(f'urls: {len(urls)} | meta: {len(meta_data)}')
        if len(urls) <= 1:
            return SimilarityResponseBatch(message=f"should at least send 2 images instead of {len(urls)}", error=True)
                
        similarity_result = self._compare_images(urls, meta_data, request.min_score)
        response = SimilarityResponseBatch(message='success', result=similarity_result)
        if request.webhook == None or request.webhook.url == '':                   
            return response    

        # Convert numpy types to native Python types for JSON serialization
        converted_result = self._convert_numpy_types(similarity_result)
        body = {'message': 'success', 'result': converted_result}      
        try:                  
            resp = requests.post(request.webhook.url, json=body)
            logging.info(f'>>> webhook sent: {request.webhook.url} | code: {resp.status_code}')
        except Exception as e:
            logging.info(f'>>> post webhook error: {request.webhook.url}  | err: {e} | body: {body}')                   
        return response

    def __compare_images_concurrency(self, url_main, urls, meta_data, min_score, result):
        if len(urls) == 0:
            return

        #extract features 
        vector_compare = {}
        threads = []        
        for i, url in enumerate(urls):
            t = threading.Thread(target=self.get_feature_concurency, args=[url, vector_compare])
            t.start()
            threads.append(t)

            if len(threads) == self.max_thread or i == len(urls)-1:
                for t in threads:
                    t.join()
                threads = []

        vector_main = {}
        self.get_feature_concurency(url_main, vector_main)
        keys = list(vector_compare.keys())
        vectors = np.array(list(vector_compare.values()))
        similars = calculate_similarities(vector_main[url_main], vectors)

        for similar, index in similars:
            if similar >= min_score:
                result[url_main] = {
                    "url": keys[index],
                    "similarity": similar,
                    "meta_data": meta_data[keys[index]]
                }

    def _compare_images(self, urls, meta_data, min_score):
        #extract features 
        result = {}
        threads = []        
        for i, url in enumerate(urls):
            t = threading.Thread(target=self.get_feature_concurency, args=[url, result])
            t.start()
            threads.append(t)

            if len(threads) == self.max_thread or i == len(urls)-1:
                print(f'>> wait {len(threads)} threads....')
                for t in threads:
                    t.join()
                print(f'>> wait finish of {len(threads)} ...')
                threads = []
        print(f'result: {len(result)}')

        similarity_result = []
        vectors = list(result.values()) 
        similarities = calculate_similarity_matrix(np.array(vectors))
        print(f'similarities shape: {similarities.shape}')
        for i, url in enumerate(result.keys()):
            similar_imgs = []
            for j, url_compare in enumerate(result.keys()):
                if i == j or similarities[i, j] < min_score: continue
                similar_imgs.append({
                    "url" : url_compare,
                    "similarity" : similarities[i, j]
                })
            if len(similar_imgs) > 0:        
                similarity_result.append({
                    "url_original": url,
                    "meta_data": meta_data[url] if url in meta_data else None,
                    "similar_images" : similar_imgs
                })
        return similarity_result

    def _parse_request(self, urls, imageUrls) -> Union[List[str], Dict]:
        if urls:  print(f'>>> start processing {len(urls)} urls...')
        elif imageUrls: print(f'>>> start processing {len(imageUrls)} imageUrls... ')
        else: 
            print(">> no img url...")
            return [], {}
            
        meta_data = {}
        if imageUrls and (urls == None or len(urls) == 0) and len(imageUrls) > 0:
            urls = []
            for img in imageUrls:
                if img.url == None or img.url == '': continue
                urls.append(img.url) 
                if img.meta_data is not None and len(img.meta_data) > 0:       
                    meta_data[img.url] = img.meta_data
        return urls, meta_data

    def _get_feature_cache(self, url):
        img_url = extract.url_only(url)
        img_feature = []            
        try:
            feature_cached =  self.repo_cache.get(img_url)
            if feature_cached is not None:                
                img_feature = np.array(ast.literal_eval(feature_cached.decode("utf-8")))                
        except Exception as e:
            print(f'got from cache {url}, err: {e}')   
        return img_feature    
